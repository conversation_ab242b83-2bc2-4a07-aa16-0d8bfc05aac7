using GamesEngine.Business.Liquidity.Persistence;
using GamesEngine.Settings;
using System.Diagnostics;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;

namespace LiquidityBIAPI
{
    public static class DataSeeder
    {
        public static void SeedInitialData(IStorage storage, string currencyCode)
        {
            try
            {
                var allSeededDepositIds = new List<long>();
                var depositsToCreate = GetDeposits();
                int newDepositsCount = 0;
                foreach (var deposit in depositsToCreate)
                {
                    storage.CreateDeposit(currencyCode, deposit);
                    allSeededDepositIds.Add(deposit.Id);
                    newDepositsCount++;
                }

                SeedJarsAndDetails(storage, currencyCode, allSeededDepositIds);
                SeedTanksAndDetails(storage, currencyCode, allSeededDepositIds);
                SeedTankersAndDetails(storage, currencyCode, allSeededDepositIds);

                SeedWithdrawalsAndDetails(storage, currencyCode);

                Debug.WriteLine("DataSeeder: Initial data seeding process completed successfully.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"DataSeeder: An error occurred during initial data seeding: {ex.Message}", ex);
                throw;
            }
        }

        private static List<Deposit> GetDeposits()
        {
            var baseDate = DateTime.Now.AddDays(-60);
            int initialID = 100000;
            return new List<Deposit>
            {
                // Group 1: AccountNumber "ACC123", various domains and dates
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP001", Amount = 150.75m, Date = baseDate.AddDays(5).AddHours(10), StoreId = 1, AccountNumber = "ACC123", DomainId = 1, Address = "101 Cherry Lane", Created = DateTime.Now.AddDays(-55) },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP002", Amount = 300.00m, Date = baseDate.AddDays(10).AddHours(11), StoreId = 2, AccountNumber = "ACC123", DomainId = 2, Address = "102 Blossom Ave", Created = DateTime.Now.AddDays(-50) },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP003", Amount = 75.50m, Date = baseDate.AddDays(15).AddHours(9), StoreId = 2, AccountNumber = "ACC123", DomainId = 2, Address = "103 Daisy Path", Created = DateTime.Now.AddDays(-45) },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP015", Amount = 25.00m,  Date = DateTime.Now.AddDays(-2).AddHours(10),  StoreId = 1, AccountNumber = "ACC123", DomainId = 1, Address = "104 Tulip Road (Recent)", Created = DateTime.Now.AddDays(-2) }, // Recent for ACC123

                // Group 2: AccountNumber "ACC456"
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP004", Amount = 1250.00m, Date = baseDate.AddDays(8).AddHours(14), StoreId = 3, AccountNumber = "ACC456", DomainId = 3, Address = "201 Oak Street", Created = DateTime.Now.AddDays(-52) },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP005", Amount = 80.20m, Date = baseDate.AddDays(20).AddHours(15), StoreId = 3, AccountNumber = "ACC456", DomainId = 3, Address = "202 Pine Way", Created = DateTime.Now.AddDays(-40) },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP016", Amount = 500.00m, Date = DateTime.Now.AddDays(-4).AddHours(11), StoreId = 2, AccountNumber = "ACC456", DomainId = 2, Address = "203 Maple Drive (Recent)", Created = DateTime.Now.AddDays(-4) }, // Recent for ACC456

                // Group 3: AccountNumber "ACC789", more recent deposits for newest jar testing
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP006", Amount = 320.00m, Date = DateTime.Now.AddDays(-7).AddHours(10), StoreId = 1, AccountNumber = "ACC789", DomainId = 1, Address = "301 Birch Blvd", Created = DateTime.Now.AddDays(-7) },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP007", Amount = 475.99m, Date = DateTime.Now.AddDays(-5).AddHours(12), StoreId = 2, AccountNumber = "ACC789", DomainId = 2, Address = "302 Cedar Court", Created = DateTime.Now.AddDays(-5) },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP008", Amount = 99.00m,  Date = DateTime.Now.AddDays(-3).AddHours(13), StoreId = 1, AccountNumber = "ACC789", DomainId = 1, Address = "303 Elm Place", Created = DateTime.Now.AddDays(-3) },

                // Miscellaneous Deposits
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP009", Amount = 15.00m,  Date = baseDate.AddDays(25).AddHours(8), StoreId = 1, AccountNumber = "ACCMISC1", DomainId = 1, Address = "401 Willow Way", Created = DateTime.Now.AddDays(-35) },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP010", Amount = 88.88m, Date = baseDate.AddDays(30).AddHours(16), StoreId = 2, AccountNumber = "ACCMISC2", DomainId = 2, Address = "402 Spruce St", Created = DateTime.Now.AddDays(-30) },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP011", Amount = 1800.00m, Date = DateTime.Now.AddDays(-1).AddHours(14), StoreId = 1, AccountNumber = "ACCBIG1", DomainId = 1, Address = "501 Aspen Circle", Created = DateTime.Now.AddDays(-1) },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP012", Amount = 5.50m, Date = baseDate.AddDays(40).AddHours(17), StoreId = 3, AccountNumber = "ACCSMALL1", DomainId = 3, Address = "601 Fir Lane", Created = DateTime.Now.AddDays(-20) },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP017", Amount = 777.00m, Date = DateTime.Now.AddDays(-6).AddHours(9), StoreId = 1, AccountNumber = "ACC789", DomainId = 1, Address = "304 Redwood Rd", Created = DateTime.Now.AddDays(-6) },
                 // Deposit with same DocumentNumber & DomainId as AD_DEP001 to test GetOrCreateDeposit
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP001", Amount = 150.75m, Date = baseDate.AddDays(5).AddHours(10), StoreId = 1, AccountNumber = "ACC123", DomainId = 1, Address = "101 Cherry Lane (Duplicate Check)", Created = DateTime.Now.AddDays(-55) },
            };
        }

        private static void SeedJarsAndDetails(IStorage storage, string currencyCode, List<long> allDepositIds)
        {
            var additionalDepositIds = allDepositIds.ToList();
            if (!additionalDepositIds.Any())
            {
                Debug.WriteLine("DataSeeder: No additional deposits to link to new jars.");
                return;
            }
            Debug.WriteLine($"DataSeeder: Seeding additional jars and linking {additionalDepositIds.Count} additional deposits.");

            Func<int, long?> getDepositIdAtIndex = (index) => additionalDepositIds.Count > index ? (long?)additionalDepositIds[index] : null;
            DateTime linkTime = DateTime.Now;

            // Jar V1 (Older version)
            int jarV1 = 1;
            storage.CreateJar(currencyCode, jarV1, "Historical Batch Jar - V1", DateTime.Now.AddDays(-50));
            TryAddJarDetail(storage, currencyCode, jarV1, getDepositIdAtIndex(0), linkTime.AddDays(-50)); // AD_DEP001
            TryAddJarDetail(storage, currencyCode, jarV1, getDepositIdAtIndex(4), linkTime.AddDays(-50)); // AD_DEP004

            // Jar V2 (Intermediate version)
            int jarV2 = 2;
            storage.CreateJar(currencyCode, jarV2, "Regular Processing Jar - V2", DateTime.Now.AddDays(-25));
            TryAddJarDetail(storage, currencyCode, jarV2, getDepositIdAtIndex(1), linkTime.AddDays(-25)); // AD_DEP002
            TryAddJarDetail(storage, currencyCode, jarV2, getDepositIdAtIndex(5), linkTime.AddDays(-25)); // AD_DEP005
            TryAddJarDetail(storage, currencyCode, jarV2, getDepositIdAtIndex(10), linkTime.AddDays(-25)); // AD_DEP009 (ACCMISC1)

            // Jar V3 (Newest Version - this should be picked by DepositsInNewestJar)
            int jarV3 = 3;
            storage.CreateJar(currencyCode, jarV3, "Current Active Jar - V3", DateTime.Now.AddDays(-2));
            TryAddJarDetail(storage, currencyCode, jarV3, getDepositIdAtIndex(7), linkTime.AddDays(-2));  // AD_DEP006 (ACC789)
            TryAddJarDetail(storage, currencyCode, jarV3, getDepositIdAtIndex(8), linkTime.AddDays(-2));  // AD_DEP007 (ACC789)
            TryAddJarDetail(storage, currencyCode, jarV3, getDepositIdAtIndex(9), linkTime.AddDays(-2));  // AD_DEP008 (ACC789)
            TryAddJarDetail(storage, currencyCode, jarV3, getDepositIdAtIndex(12), linkTime.AddDays(-2)); // AD_DEP011 (ACCBIG1, recent)
            TryAddJarDetail(storage, currencyCode, jarV3, getDepositIdAtIndex(3), linkTime.AddDays(-2));  // AD_DEP015 (ACC123, recent)
            TryAddJarDetail(storage, currencyCode, jarV3, getDepositIdAtIndex(6), linkTime.AddDays(-2));  // AD_DEP016 (ACC456, recent)
            TryAddJarDetail(storage, currencyCode, jarV3, getDepositIdAtIndex(14), linkTime.AddDays(-2)); // AD_DEP017 (ACC789, recent)

            Debug.WriteLine("DataSeeder: Finished seeding additional Jars and JarDetails.");
        }

        private static void TryAddJarDetail(IStorage storage, string currencyCode, long jarId, long? depositId, DateTime created)
        {
            if (depositId.HasValue)
            {
                storage.CreateJarDetailIfNotExists(currencyCode, jarId, depositId.Value, created);
                Debug.WriteLine($"DataSeeder: Linked Deposit ID {depositId.Value} to Jar ID {jarId}");
            }
        }

        private static void SeedTanksAndDetails(IStorage storage, string currencyCode, List<long> allDepositIds)
        {
            var additionalDepositIds = allDepositIds.ToList();
            if (!additionalDepositIds.Any())
            {
                Debug.WriteLine("DataSeeder: No additional deposits to link to new tanks.");
                return;
            }
            Debug.WriteLine($"DataSeeder: Seeding additional tanks and linking {additionalDepositIds.Count} additional deposits.");

            Func<int, long?> getDepositIdAtIndex = (index) => additionalDepositIds.Count > index ? (long?)additionalDepositIds[index] : null;
            DateTime linkTime = DateTime.Now;

            long tank1Id = 1000000;
            storage.CreateTank(currencyCode, new Tank { Id = tank1Id, Description = "Primary Holding Tank A (New)", Created = DateTime.Now.AddDays(-40) });
            TryAddTankDetail(storage, currencyCode, tank1Id, getDepositIdAtIndex(0), linkTime.AddDays(-40)); // AD_DEP001 (ACC123)
            TryAddTankDetail(storage, currencyCode, tank1Id, getDepositIdAtIndex(1), linkTime.AddDays(-40)); // AD_DEP002 (ACC123)
            TryAddTankDetail(storage, currencyCode, tank1Id, getDepositIdAtIndex(7), linkTime.AddDays(-40)); // AD_DEP006 (ACC789)

            long tank2Id = 1000001; 
            storage.CreateTank(currencyCode, new Tank { Id = tank2Id, Description = "Secondary Processing Tank B (New)", Created = DateTime.Now.AddDays(-10) });
            TryAddTankDetail(storage, currencyCode, tank2Id, getDepositIdAtIndex(2), linkTime.AddDays(-10)); // AD_DEP003 (ACC123)
            TryAddTankDetail(storage, currencyCode, tank2Id, getDepositIdAtIndex(3), linkTime.AddDays(-10)); // AD_DEP015 (ACC123 recent)
            TryAddTankDetail(storage, currencyCode, tank2Id, getDepositIdAtIndex(8), linkTime.AddDays(-10)); // AD_DEP007 (ACC789)
            TryAddTankDetail(storage, currencyCode, tank2Id, getDepositIdAtIndex(12), linkTime.AddDays(-10)); // AD_DEP011 (ACCBIG1)

            long tank3Id = 1000002; 
            storage.CreateTank(currencyCode, new Tank { Id = tank3Id, Description = "Empty Reserve Tank C (New)", Created = DateTime.Now.AddDays(-5) });
            // No deposits initially for testing empty tank scenarios
            Debug.WriteLine($"DataSeeder: Created Tank ID {tank3Id} (Empty Reserve Tank C (New)) with no deposits.");

            Debug.WriteLine("DataSeeder: Finished seeding additional Tanks and TankDetails.");
        }

        private static void TryAddTankDetail(IStorage storage, string currencyCode, long tankId, long? depositId, DateTime created)
        {
            if (depositId.HasValue)
            {
                storage.CreateTankDetailIfNotExists(currencyCode, tankId, depositId.Value, created);
                Debug.WriteLine($"DataSeeder: Linked Deposit ID {depositId.Value} to Tank ID {tankId}");
            }
        }

        private static void SeedTankersAndDetails(IStorage storage, string currencyCode, List<long> allDepositIds)
        {
            if (!allDepositIds.Any())
            {
                Debug.WriteLine("DataSeeder: No additional deposits to link to new tankers.");
                return;
            }
            Debug.WriteLine($"DataSeeder: Seeding additional tankers and linking {allDepositIds.Count} additional deposits.");

            Func<int, long?> getDepositIdAtIndex = (index) => allDepositIds.Count > index ? (long?)allDepositIds[index] : null;
            DateTime linkTime = DateTime.Now;

            long tanker1Id = 1000000;
            storage.CreateTanker(currencyCode, new Tanker { Id = tanker1Id, Description = "Main Distribution Tanker X1 (New)", Created = DateTime.Now.AddDays(-30) });
            TryAddTankerDetail(storage, currencyCode, tanker1Id, getDepositIdAtIndex(4), linkTime.AddDays(-30)); // AD_DEP004 (ACC456)
            TryAddTankerDetail(storage, currencyCode, tanker1Id, getDepositIdAtIndex(5), linkTime.AddDays(-30)); // AD_DEP005 (ACC456)
            TryAddTankerDetail(storage, currencyCode, tanker1Id, getDepositIdAtIndex(9), linkTime.AddDays(-30)); // AD_DEP008 (ACC789)

            long tanker2Id = 1000001;
            storage.CreateTanker(currencyCode, new Tanker { Id = tanker2Id, Description = "Regional Supply Tanker Y2 (New)", Created = DateTime.Now.AddDays(-8) });
            TryAddTankerDetail(storage, currencyCode, tanker2Id, getDepositIdAtIndex(6), linkTime.AddDays(-8));  // AD_DEP016 (ACC456 recent)
            TryAddTankerDetail(storage, currencyCode, tanker2Id, getDepositIdAtIndex(10), linkTime.AddDays(-8)); // AD_DEP009 (ACCMISC1)
            TryAddTankerDetail(storage, currencyCode, tanker2Id, getDepositIdAtIndex(11), linkTime.AddDays(-8)); // AD_DEP010 (ACCMISC2)
            TryAddTankerDetail(storage, currencyCode, tanker2Id, getDepositIdAtIndex(14), linkTime.AddDays(-8)); // AD_DEP017 (ACC789 recent)

            long tanker3Id = 1000002;
            storage.CreateTanker(currencyCode, new Tanker { Id = tanker3Id, Description = "Standby Tanker Z3 (New)", Created = DateTime.Now.AddDays(-3) });
            TryAddTankerDetail(storage, currencyCode, tanker3Id, getDepositIdAtIndex(0), linkTime.AddDays(-3)); // Link AD_DEP001 for some data
            Debug.WriteLine($"DataSeeder: Created Tanker ID {tanker3Id} (Standby Tanker Z3 (New)).");

            Debug.WriteLine("DataSeeder: Finished seeding additional Tankers and TankerDetails.");
        }

        private static void TryAddTankerDetail(IStorage storage, string currencyCode, long tankerId, long? depositId, DateTime created)
        {
            if (depositId.HasValue)
            {
                storage.CreateTankerDetailIfNotExists(currencyCode, tankerId, depositId.Value, created);
                Debug.WriteLine($"DataSeeder: Linked Deposit ID {depositId.Value} to Tanker ID {tankerId}");
            }
        }

        private static void SeedWithdrawalsAndDetails(IStorage storage, string currencyCode)
        {
            Debug.WriteLine("DataSeeder: Seeding example Withdrawals, Bottles, Dispensers, and their details.");

            int initialID = 100000;
            var withdrawals = new List<Withdrawal>
            {
                new Withdrawal { Id = initialID++, DocumentNumber = "WD001", Amount = 100.00m, Date = DateTime.Now.AddDays(-3), StoreId = 1, AccountNumber = "ACC123", DomainId = 1, Address = "WD Address 1", Created = DateTime.Now.AddDays(-3) },
                new Withdrawal { Id = initialID++, DocumentNumber = "WD002", Amount = 50.50m, Date = DateTime.Now.AddDays(-2), StoreId = 2, AccountNumber = "ACC456", DomainId = 2, Address = "WD Address 2", Created = DateTime.Now.AddDays(-2) }
            };

            var withdrawalIds = new List<long>();
            foreach (var wd in withdrawals)
            {
                storage.CreateWithdrawal(currencyCode, wd);
                withdrawalIds.Add(wd.Id);
            }

            int bottleId = 100000;
            var bottles = new List<Bottle>
            {
                new Bottle { Id = bottleId++, Description = "Bottle A", Created = DateTime.Now.AddDays(-3) },
                new Bottle { Id = bottleId++, Description = "Bottle B", Created = DateTime.Now.AddDays(-2) }
            };
            var bottleIds = new List<long>();
            foreach (var b in bottles)
            {
                storage.CreateBottle(currencyCode, b);
                bottleIds.Add(b.Id);
            }

            int dispenserId = 100000;
            var dispensers = new List<Dispenser>
            {
                new Dispenser { Id = dispenserId++, Description = "Dispenser X", Created = DateTime.Now.AddDays(-3) },
                new Dispenser { Id = dispenserId++, Description = "Dispenser Y", Created = DateTime.Now.AddDays(-2) }
            };
            var dispenserIds = new List<long>();
            foreach (var d in dispensers)
            {
                storage.CreateDispenser(currencyCode, d);
                dispenserIds.Add(d.Id);
            }

            if (withdrawalIds.Count > 0 && bottleIds.Count > 0)
            {
                storage.CreateBottleDetailIfNotExists(currencyCode, withdrawalIds[0], bottleIds[0], DateTime.Now.AddDays(-3));
                storage.CreateBottleDetailIfNotExists(currencyCode, withdrawalIds[1], bottleIds[1], DateTime.Now.AddDays(-2));
            }

            if (withdrawalIds.Count > 0 && dispenserIds.Count > 0)
            {
                storage.CreateDispenserDetailIfNotExists(currencyCode, withdrawalIds[0], dispenserIds[0], DateTime.Now.AddDays(-3));
                storage.CreateDispenserDetailIfNotExists(currencyCode, withdrawalIds[1], dispenserIds[1], DateTime.Now.AddDays(-2));
            }

            Debug.WriteLine("DataSeeder: Finished seeding Withdrawals, Bottles, Dispensers, and their details.");
        }
    }
}